{"version": 3, "file": "websocketService.d.ts", "sourceRoot": "", "sources": ["../../src/services/websocketService.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAIH,OAAO,EAAE,YAAY,EAAE,MAAM,6BAA6B,CAAC;AAI3D,MAAM,WAAW,gBAAgB;IAC/B,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,GAAG,CAAC;IACV,SAAS,EAAE,MAAM,CAAC;CACnB;AAED,MAAM,WAAW,gBAAgB;IAC/B,aAAa,EAAE,MAAM,CAAC;IACtB,SAAS,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;IAC5B,MAAM,EAAE,MAAM,CAAC;IACf,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,eAAe,EAAE,MAAM,CAAC;IACxB,SAAS,EAAE,MAAM,CAAC;IAClB,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CAChC;AAED,MAAM,WAAW,gBAAgB;IAC/B,aAAa,EAAE,MAAM,CAAC;IACtB,WAAW,EAAE,UAAU,GAAG,UAAU,CAAC;IACrC,cAAc,EAAE,MAAM,CAAC;IACvB,MAAM,EAAE,MAAM,GAAG,MAAM,GAAG,KAAK,CAAC;CACjC;AAED,cAAM,gBAAgB;IACpB,OAAO,CAAC,GAAG,CAAgC;IAC3C,OAAO,CAAC,OAAO,CAA6B;IAE5C;;OAEG;IACH,UAAU,CAAC,MAAM,EAAE,GAAG,GAAG,IAAI;IA6C7B;;OAEG;IACH,OAAO,CAAC,mBAAmB;IAiC3B;;OAEG;IACH,OAAO,CAAC,mBAAmB;IA+B3B;;OAEG;IACH,OAAO,CAAC,YAAY;IAUpB;;OAEG;IACH,SAAS,CAAC,OAAO,EAAE,gBAAgB,GAAG,IAAI;IAgC1C;;OAEG;IACH,qBAAqB,CAAC,SAAS,EAAE,gBAAgB,GAAG,IAAI;IAQxD;;OAEG;IACH,0BAA0B,CAAC,WAAW,EAAE,YAAY,GAAG,IAAI;IAiB3D;;OAEG;IACH,qBAAqB,CAAC,SAAS,EAAE,gBAAgB,GAAG,IAAI;IAQxD;;OAEG;IACH,yBAAyB,CAAC,SAAS,EAAE;QACnC,SAAS,EAAE,MAAM,CAAC;QAClB,gBAAgB,EAAE,MAAM,CAAC;QACzB,aAAa,EAAE,MAAM,CAAC;QACtB,SAAS,EAAE,MAAM,CAAC;QAClB,MAAM,EAAE,MAAM,CAAC;QACf,QAAQ,EAAE,MAAM,CAAC;QACjB,SAAS,EAAE,OAAO,CAAC;KACpB,GAAG,IAAI;IAQR;;OAEG;IACH,WAAW,IAAI,IAAI;IA0BnB;;OAEG;IACH,QAAQ,IAAI;QAAE,gBAAgB,EAAE,MAAM,CAAC;QAAC,aAAa,EAAE,OAAO,CAAA;KAAE;IAOhE;;OAEG;IACH,OAAO,CAAC,gBAAgB;IAIxB;;OAEG;IACH,KAAK,IAAI,IAAI;CAQd;AAED,eAAO,MAAM,gBAAgB,kBAAyB,CAAC"}