{"name": "backend", "version": "1.0.0", "private": true, "main": "dist/server.js", "scripts": {"build": "tsc -p tsconfig.build.json", "dev": "tsc-watch --onSuccess \"node dist/server.js\" --onFailure \"echo Build Failed\"", "start": "node dist/server.js", "start:prod": "NODE_ENV=production node dist/server.js", "lint": "eslint . --ext .ts --fix", "lint:check": "eslint . --ext .ts", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "clean": "rm -rf dist", "square:locations": "node scripts/fetch-square-locations.js", "square:locations:ts": "npx tsx scripts/fetch-square-locations.ts"}, "dependencies": {"@fastify/cors": "^9.0.1", "@fastify/helmet": "^11.1.1", "@fastify/rate-limit": "^9.1.0", "@fastify/websocket": "^11.2.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.6", "@types/node-cron": "^3.0.11", "@types/ws": "^8.18.1", "axios": "^1.6.8", "bcryptjs": "^2.4.3", "dotenv": "^16.4.5", "fastify": "^4.26.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.3.0", "node-cron": "^4.2.1", "pino": "^8.19.0", "pino-pretty": "^11.0.0", "square": "^39.1.1", "stripe": "^15.1.0", "typescript": "^5.4.5", "ws": "^8.18.3", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^24.2.1", "@vitest/coverage-v8": "^1.5.0", "eslint": "^8.57.0", "mongodb-memory-server": "^9.1.8", "tsc-watch": "^6.2.0", "vitest": "^1.5.0"}}