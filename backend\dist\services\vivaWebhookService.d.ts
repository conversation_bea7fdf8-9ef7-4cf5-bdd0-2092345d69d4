/**
 * Viva Wallet Webhook Service
 *
 * Handles Viva Wallet webhook verification and event processing
 * Supports webhook verification key generation and signature validation
 */
import { z } from 'zod';
export declare const VIVA_WEBHOOK_EVENTS: {
    readonly TRANSACTION_PAYMENT_CREATED: 1796;
    readonly TRANSACTION_FAILED: 1798;
    readonly TRANSACTION_PRICE_CALCULATED: 1799;
    readonly TRANSACTION_REVERSAL_CREATED: 1797;
    readonly ACCOUNT_TRANSACTION_CREATED: 2054;
    readonly COMMAND_BANK_TRANSFER_CREATED: 768;
    readonly COMMAND_BANK_TRANSFER_EXECUTED: 769;
    readonly ACCOUNT_CONNECTED: 8193;
    readonly ACCOUNT_VERIFICATION_STATUS_CHANGED: 8194;
    readonly TRANSFER_CREATED: 8448;
    readonly TRANSACTION_POS_ECR_SESSION_CREATED: 1802;
    readonly TRANSACTION_POS_ECR_SESSION_FAILED: 1803;
};
declare const vivaTransactionEventDataSchema: z.ZodObject<{
    Moto: z.ZodOptional<z.ZodBoolean>;
    BinId: z.ZodOptional<z.ZodNumber>;
    IsDcc: z.ZodOptional<z.ZodBoolean>;
    Ucaf: z.ZodOptional<z.ZodString>;
    Email: z.ZodOptional<z.ZodString>;
    Phone: z.ZodOptional<z.ZodString>;
    BankId: z.ZodOptional<z.ZodString>;
    Systemic: z.ZodOptional<z.ZodBoolean>;
    BatchId: z.ZodOptional<z.ZodString>;
    Switching: z.ZodOptional<z.ZodBoolean>;
    ParentId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    Amount: z.ZodNumber;
    ChannelId: z.ZodOptional<z.ZodString>;
    TerminalId: z.ZodOptional<z.ZodNumber>;
    MerchantId: z.ZodString;
    OrderCode: z.ZodNumber;
    ProductId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    StatusId: z.ZodString;
    FullName: z.ZodOptional<z.ZodString>;
    ResellerId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    DualMessage: z.ZodOptional<z.ZodBoolean>;
    InsDate: z.ZodString;
    TotalFee: z.ZodOptional<z.ZodNumber>;
    CardToken: z.ZodOptional<z.ZodString>;
    CardNumber: z.ZodOptional<z.ZodString>;
    Descriptor: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    TipAmount: z.ZodOptional<z.ZodNumber>;
    SourceCode: z.ZodOptional<z.ZodString>;
    SourceName: z.ZodOptional<z.ZodString>;
    Latitude: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
    Longitude: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
    CompanyName: z.ZodOptional<z.ZodString>;
    TransactionId: z.ZodString;
    CompanyTitle: z.ZodOptional<z.ZodString>;
    PanEntryMode: z.ZodOptional<z.ZodString>;
    ReferenceNumber: z.ZodOptional<z.ZodNumber>;
    ResponseCode: z.ZodOptional<z.ZodString>;
    CurrencyCode: z.ZodOptional<z.ZodString>;
    OrderCulture: z.ZodOptional<z.ZodString>;
    MerchantTrns: z.ZodOptional<z.ZodString>;
    CustomerTrns: z.ZodOptional<z.ZodString>;
    IsManualRefund: z.ZodOptional<z.ZodBoolean>;
    TargetPersonId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    TargetWalletId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    AcquirerApproved: z.ZodOptional<z.ZodBoolean>;
    LoyaltyTriggered: z.ZodOptional<z.ZodBoolean>;
    TransactionTypeId: z.ZodOptional<z.ZodNumber>;
    AuthorizationId: z.ZodOptional<z.ZodString>;
    TotalInstallments: z.ZodOptional<z.ZodNumber>;
    CardCountryCode: z.ZodOptional<z.ZodString>;
    CardIssuingBank: z.ZodOptional<z.ZodString>;
    RedeemedAmount: z.ZodOptional<z.ZodNumber>;
    ClearanceDate: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    ConversionRate: z.ZodOptional<z.ZodNumber>;
    CurrentInstallment: z.ZodOptional<z.ZodNumber>;
    OriginalAmount: z.ZodOptional<z.ZodNumber>;
    Tags: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    BillId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    ConnectedAccountId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    ResellerSourceCode: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    ResellerSourceName: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    MerchantCategoryCode: z.ZodOptional<z.ZodNumber>;
    ResellerCompanyName: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    CardUniqueReference: z.ZodOptional<z.ZodString>;
    OriginalCurrencyCode: z.ZodOptional<z.ZodString>;
    ExternalTransactionId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    ResellerSourceAddress: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    CardExpirationDate: z.ZodOptional<z.ZodString>;
    ServiceId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    RetrievalReferenceNumber: z.ZodOptional<z.ZodString>;
    AssignedMerchantUsers: z.ZodOptional<z.ZodArray<z.ZodAny, "many">>;
    AssignedResellerUsers: z.ZodOptional<z.ZodArray<z.ZodAny, "many">>;
    CardTypeId: z.ZodOptional<z.ZodNumber>;
    ResponseEventId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    ElectronicCommerceIndicator: z.ZodOptional<z.ZodString>;
    OrderServiceId: z.ZodOptional<z.ZodNumber>;
    ApplicationIdentifierTerminal: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    IntegrationId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    CardProductCategoryId: z.ZodOptional<z.ZodNumber>;
    CardProductAccountTypeId: z.ZodOptional<z.ZodNumber>;
    DigitalWalletId: z.ZodOptional<z.ZodNumber>;
    DccSessionId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    DccMarkup: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
    DccDifferenceOverEcb: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
}, "strip", z.ZodTypeAny, {
    Moto?: boolean;
    BinId?: number;
    IsDcc?: boolean;
    Ucaf?: string;
    Email?: string;
    Phone?: string;
    BankId?: string;
    Systemic?: boolean;
    BatchId?: string;
    Switching?: boolean;
    ParentId?: string;
    Amount?: number;
    ChannelId?: string;
    TerminalId?: number;
    MerchantId?: string;
    OrderCode?: number;
    ProductId?: string;
    StatusId?: string;
    FullName?: string;
    ResellerId?: string;
    DualMessage?: boolean;
    InsDate?: string;
    TotalFee?: number;
    CardToken?: string;
    CardNumber?: string;
    Descriptor?: string;
    TipAmount?: number;
    SourceCode?: string;
    SourceName?: string;
    Latitude?: number;
    Longitude?: number;
    CompanyName?: string;
    TransactionId?: string;
    CompanyTitle?: string;
    PanEntryMode?: string;
    ReferenceNumber?: number;
    ResponseCode?: string;
    CurrencyCode?: string;
    OrderCulture?: string;
    MerchantTrns?: string;
    CustomerTrns?: string;
    IsManualRefund?: boolean;
    TargetPersonId?: string;
    TargetWalletId?: string;
    AcquirerApproved?: boolean;
    LoyaltyTriggered?: boolean;
    TransactionTypeId?: number;
    AuthorizationId?: string;
    TotalInstallments?: number;
    CardCountryCode?: string;
    CardIssuingBank?: string;
    RedeemedAmount?: number;
    ClearanceDate?: string;
    ConversionRate?: number;
    CurrentInstallment?: number;
    OriginalAmount?: number;
    Tags?: string[];
    BillId?: string;
    ConnectedAccountId?: string;
    ResellerSourceCode?: string;
    ResellerSourceName?: string;
    MerchantCategoryCode?: number;
    ResellerCompanyName?: string;
    CardUniqueReference?: string;
    OriginalCurrencyCode?: string;
    ExternalTransactionId?: string;
    ResellerSourceAddress?: string;
    CardExpirationDate?: string;
    ServiceId?: string;
    RetrievalReferenceNumber?: string;
    AssignedMerchantUsers?: any[];
    AssignedResellerUsers?: any[];
    CardTypeId?: number;
    ResponseEventId?: string;
    ElectronicCommerceIndicator?: string;
    OrderServiceId?: number;
    ApplicationIdentifierTerminal?: string;
    IntegrationId?: string;
    CardProductCategoryId?: number;
    CardProductAccountTypeId?: number;
    DigitalWalletId?: number;
    DccSessionId?: string;
    DccMarkup?: number;
    DccDifferenceOverEcb?: number;
}, {
    Moto?: boolean;
    BinId?: number;
    IsDcc?: boolean;
    Ucaf?: string;
    Email?: string;
    Phone?: string;
    BankId?: string;
    Systemic?: boolean;
    BatchId?: string;
    Switching?: boolean;
    ParentId?: string;
    Amount?: number;
    ChannelId?: string;
    TerminalId?: number;
    MerchantId?: string;
    OrderCode?: number;
    ProductId?: string;
    StatusId?: string;
    FullName?: string;
    ResellerId?: string;
    DualMessage?: boolean;
    InsDate?: string;
    TotalFee?: number;
    CardToken?: string;
    CardNumber?: string;
    Descriptor?: string;
    TipAmount?: number;
    SourceCode?: string;
    SourceName?: string;
    Latitude?: number;
    Longitude?: number;
    CompanyName?: string;
    TransactionId?: string;
    CompanyTitle?: string;
    PanEntryMode?: string;
    ReferenceNumber?: number;
    ResponseCode?: string;
    CurrencyCode?: string;
    OrderCulture?: string;
    MerchantTrns?: string;
    CustomerTrns?: string;
    IsManualRefund?: boolean;
    TargetPersonId?: string;
    TargetWalletId?: string;
    AcquirerApproved?: boolean;
    LoyaltyTriggered?: boolean;
    TransactionTypeId?: number;
    AuthorizationId?: string;
    TotalInstallments?: number;
    CardCountryCode?: string;
    CardIssuingBank?: string;
    RedeemedAmount?: number;
    ClearanceDate?: string;
    ConversionRate?: number;
    CurrentInstallment?: number;
    OriginalAmount?: number;
    Tags?: string[];
    BillId?: string;
    ConnectedAccountId?: string;
    ResellerSourceCode?: string;
    ResellerSourceName?: string;
    MerchantCategoryCode?: number;
    ResellerCompanyName?: string;
    CardUniqueReference?: string;
    OriginalCurrencyCode?: string;
    ExternalTransactionId?: string;
    ResellerSourceAddress?: string;
    CardExpirationDate?: string;
    ServiceId?: string;
    RetrievalReferenceNumber?: string;
    AssignedMerchantUsers?: any[];
    AssignedResellerUsers?: any[];
    CardTypeId?: number;
    ResponseEventId?: string;
    ElectronicCommerceIndicator?: string;
    OrderServiceId?: number;
    ApplicationIdentifierTerminal?: string;
    IntegrationId?: string;
    CardProductCategoryId?: number;
    CardProductAccountTypeId?: number;
    DigitalWalletId?: number;
    DccSessionId?: string;
    DccMarkup?: number;
    DccDifferenceOverEcb?: number;
}>;
declare const vivaWebhookEventSchema: z.ZodObject<{
    Url: z.ZodString;
    EventData: z.ZodObject<{
        Moto: z.ZodOptional<z.ZodBoolean>;
        BinId: z.ZodOptional<z.ZodNumber>;
        IsDcc: z.ZodOptional<z.ZodBoolean>;
        Ucaf: z.ZodOptional<z.ZodString>;
        Email: z.ZodOptional<z.ZodString>;
        Phone: z.ZodOptional<z.ZodString>;
        BankId: z.ZodOptional<z.ZodString>;
        Systemic: z.ZodOptional<z.ZodBoolean>;
        BatchId: z.ZodOptional<z.ZodString>;
        Switching: z.ZodOptional<z.ZodBoolean>;
        ParentId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        Amount: z.ZodNumber;
        ChannelId: z.ZodOptional<z.ZodString>;
        TerminalId: z.ZodOptional<z.ZodNumber>;
        MerchantId: z.ZodString;
        OrderCode: z.ZodNumber;
        ProductId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        StatusId: z.ZodString;
        FullName: z.ZodOptional<z.ZodString>;
        ResellerId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        DualMessage: z.ZodOptional<z.ZodBoolean>;
        InsDate: z.ZodString;
        TotalFee: z.ZodOptional<z.ZodNumber>;
        CardToken: z.ZodOptional<z.ZodString>;
        CardNumber: z.ZodOptional<z.ZodString>;
        Descriptor: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        TipAmount: z.ZodOptional<z.ZodNumber>;
        SourceCode: z.ZodOptional<z.ZodString>;
        SourceName: z.ZodOptional<z.ZodString>;
        Latitude: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
        Longitude: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
        CompanyName: z.ZodOptional<z.ZodString>;
        TransactionId: z.ZodString;
        CompanyTitle: z.ZodOptional<z.ZodString>;
        PanEntryMode: z.ZodOptional<z.ZodString>;
        ReferenceNumber: z.ZodOptional<z.ZodNumber>;
        ResponseCode: z.ZodOptional<z.ZodString>;
        CurrencyCode: z.ZodOptional<z.ZodString>;
        OrderCulture: z.ZodOptional<z.ZodString>;
        MerchantTrns: z.ZodOptional<z.ZodString>;
        CustomerTrns: z.ZodOptional<z.ZodString>;
        IsManualRefund: z.ZodOptional<z.ZodBoolean>;
        TargetPersonId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        TargetWalletId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        AcquirerApproved: z.ZodOptional<z.ZodBoolean>;
        LoyaltyTriggered: z.ZodOptional<z.ZodBoolean>;
        TransactionTypeId: z.ZodOptional<z.ZodNumber>;
        AuthorizationId: z.ZodOptional<z.ZodString>;
        TotalInstallments: z.ZodOptional<z.ZodNumber>;
        CardCountryCode: z.ZodOptional<z.ZodString>;
        CardIssuingBank: z.ZodOptional<z.ZodString>;
        RedeemedAmount: z.ZodOptional<z.ZodNumber>;
        ClearanceDate: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        ConversionRate: z.ZodOptional<z.ZodNumber>;
        CurrentInstallment: z.ZodOptional<z.ZodNumber>;
        OriginalAmount: z.ZodOptional<z.ZodNumber>;
        Tags: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        BillId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        ConnectedAccountId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        ResellerSourceCode: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        ResellerSourceName: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        MerchantCategoryCode: z.ZodOptional<z.ZodNumber>;
        ResellerCompanyName: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        CardUniqueReference: z.ZodOptional<z.ZodString>;
        OriginalCurrencyCode: z.ZodOptional<z.ZodString>;
        ExternalTransactionId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        ResellerSourceAddress: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        CardExpirationDate: z.ZodOptional<z.ZodString>;
        ServiceId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        RetrievalReferenceNumber: z.ZodOptional<z.ZodString>;
        AssignedMerchantUsers: z.ZodOptional<z.ZodArray<z.ZodAny, "many">>;
        AssignedResellerUsers: z.ZodOptional<z.ZodArray<z.ZodAny, "many">>;
        CardTypeId: z.ZodOptional<z.ZodNumber>;
        ResponseEventId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        ElectronicCommerceIndicator: z.ZodOptional<z.ZodString>;
        OrderServiceId: z.ZodOptional<z.ZodNumber>;
        ApplicationIdentifierTerminal: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        IntegrationId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        CardProductCategoryId: z.ZodOptional<z.ZodNumber>;
        CardProductAccountTypeId: z.ZodOptional<z.ZodNumber>;
        DigitalWalletId: z.ZodOptional<z.ZodNumber>;
        DccSessionId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        DccMarkup: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
        DccDifferenceOverEcb: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
    }, "strip", z.ZodTypeAny, {
        Moto?: boolean;
        BinId?: number;
        IsDcc?: boolean;
        Ucaf?: string;
        Email?: string;
        Phone?: string;
        BankId?: string;
        Systemic?: boolean;
        BatchId?: string;
        Switching?: boolean;
        ParentId?: string;
        Amount?: number;
        ChannelId?: string;
        TerminalId?: number;
        MerchantId?: string;
        OrderCode?: number;
        ProductId?: string;
        StatusId?: string;
        FullName?: string;
        ResellerId?: string;
        DualMessage?: boolean;
        InsDate?: string;
        TotalFee?: number;
        CardToken?: string;
        CardNumber?: string;
        Descriptor?: string;
        TipAmount?: number;
        SourceCode?: string;
        SourceName?: string;
        Latitude?: number;
        Longitude?: number;
        CompanyName?: string;
        TransactionId?: string;
        CompanyTitle?: string;
        PanEntryMode?: string;
        ReferenceNumber?: number;
        ResponseCode?: string;
        CurrencyCode?: string;
        OrderCulture?: string;
        MerchantTrns?: string;
        CustomerTrns?: string;
        IsManualRefund?: boolean;
        TargetPersonId?: string;
        TargetWalletId?: string;
        AcquirerApproved?: boolean;
        LoyaltyTriggered?: boolean;
        TransactionTypeId?: number;
        AuthorizationId?: string;
        TotalInstallments?: number;
        CardCountryCode?: string;
        CardIssuingBank?: string;
        RedeemedAmount?: number;
        ClearanceDate?: string;
        ConversionRate?: number;
        CurrentInstallment?: number;
        OriginalAmount?: number;
        Tags?: string[];
        BillId?: string;
        ConnectedAccountId?: string;
        ResellerSourceCode?: string;
        ResellerSourceName?: string;
        MerchantCategoryCode?: number;
        ResellerCompanyName?: string;
        CardUniqueReference?: string;
        OriginalCurrencyCode?: string;
        ExternalTransactionId?: string;
        ResellerSourceAddress?: string;
        CardExpirationDate?: string;
        ServiceId?: string;
        RetrievalReferenceNumber?: string;
        AssignedMerchantUsers?: any[];
        AssignedResellerUsers?: any[];
        CardTypeId?: number;
        ResponseEventId?: string;
        ElectronicCommerceIndicator?: string;
        OrderServiceId?: number;
        ApplicationIdentifierTerminal?: string;
        IntegrationId?: string;
        CardProductCategoryId?: number;
        CardProductAccountTypeId?: number;
        DigitalWalletId?: number;
        DccSessionId?: string;
        DccMarkup?: number;
        DccDifferenceOverEcb?: number;
    }, {
        Moto?: boolean;
        BinId?: number;
        IsDcc?: boolean;
        Ucaf?: string;
        Email?: string;
        Phone?: string;
        BankId?: string;
        Systemic?: boolean;
        BatchId?: string;
        Switching?: boolean;
        ParentId?: string;
        Amount?: number;
        ChannelId?: string;
        TerminalId?: number;
        MerchantId?: string;
        OrderCode?: number;
        ProductId?: string;
        StatusId?: string;
        FullName?: string;
        ResellerId?: string;
        DualMessage?: boolean;
        InsDate?: string;
        TotalFee?: number;
        CardToken?: string;
        CardNumber?: string;
        Descriptor?: string;
        TipAmount?: number;
        SourceCode?: string;
        SourceName?: string;
        Latitude?: number;
        Longitude?: number;
        CompanyName?: string;
        TransactionId?: string;
        CompanyTitle?: string;
        PanEntryMode?: string;
        ReferenceNumber?: number;
        ResponseCode?: string;
        CurrencyCode?: string;
        OrderCulture?: string;
        MerchantTrns?: string;
        CustomerTrns?: string;
        IsManualRefund?: boolean;
        TargetPersonId?: string;
        TargetWalletId?: string;
        AcquirerApproved?: boolean;
        LoyaltyTriggered?: boolean;
        TransactionTypeId?: number;
        AuthorizationId?: string;
        TotalInstallments?: number;
        CardCountryCode?: string;
        CardIssuingBank?: string;
        RedeemedAmount?: number;
        ClearanceDate?: string;
        ConversionRate?: number;
        CurrentInstallment?: number;
        OriginalAmount?: number;
        Tags?: string[];
        BillId?: string;
        ConnectedAccountId?: string;
        ResellerSourceCode?: string;
        ResellerSourceName?: string;
        MerchantCategoryCode?: number;
        ResellerCompanyName?: string;
        CardUniqueReference?: string;
        OriginalCurrencyCode?: string;
        ExternalTransactionId?: string;
        ResellerSourceAddress?: string;
        CardExpirationDate?: string;
        ServiceId?: string;
        RetrievalReferenceNumber?: string;
        AssignedMerchantUsers?: any[];
        AssignedResellerUsers?: any[];
        CardTypeId?: number;
        ResponseEventId?: string;
        ElectronicCommerceIndicator?: string;
        OrderServiceId?: number;
        ApplicationIdentifierTerminal?: string;
        IntegrationId?: string;
        CardProductCategoryId?: number;
        CardProductAccountTypeId?: number;
        DigitalWalletId?: number;
        DccSessionId?: string;
        DccMarkup?: number;
        DccDifferenceOverEcb?: number;
    }>;
    Created: z.ZodString;
    CorrelationId: z.ZodString;
    EventTypeId: z.ZodNumber;
    Delay: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
    RetryCount: z.ZodNumber;
    RetryDelayInSeconds: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
    MessageId: z.ZodString;
    RecipientId: z.ZodString;
    MessageTypeId: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    Url?: string;
    EventData?: {
        Moto?: boolean;
        BinId?: number;
        IsDcc?: boolean;
        Ucaf?: string;
        Email?: string;
        Phone?: string;
        BankId?: string;
        Systemic?: boolean;
        BatchId?: string;
        Switching?: boolean;
        ParentId?: string;
        Amount?: number;
        ChannelId?: string;
        TerminalId?: number;
        MerchantId?: string;
        OrderCode?: number;
        ProductId?: string;
        StatusId?: string;
        FullName?: string;
        ResellerId?: string;
        DualMessage?: boolean;
        InsDate?: string;
        TotalFee?: number;
        CardToken?: string;
        CardNumber?: string;
        Descriptor?: string;
        TipAmount?: number;
        SourceCode?: string;
        SourceName?: string;
        Latitude?: number;
        Longitude?: number;
        CompanyName?: string;
        TransactionId?: string;
        CompanyTitle?: string;
        PanEntryMode?: string;
        ReferenceNumber?: number;
        ResponseCode?: string;
        CurrencyCode?: string;
        OrderCulture?: string;
        MerchantTrns?: string;
        CustomerTrns?: string;
        IsManualRefund?: boolean;
        TargetPersonId?: string;
        TargetWalletId?: string;
        AcquirerApproved?: boolean;
        LoyaltyTriggered?: boolean;
        TransactionTypeId?: number;
        AuthorizationId?: string;
        TotalInstallments?: number;
        CardCountryCode?: string;
        CardIssuingBank?: string;
        RedeemedAmount?: number;
        ClearanceDate?: string;
        ConversionRate?: number;
        CurrentInstallment?: number;
        OriginalAmount?: number;
        Tags?: string[];
        BillId?: string;
        ConnectedAccountId?: string;
        ResellerSourceCode?: string;
        ResellerSourceName?: string;
        MerchantCategoryCode?: number;
        ResellerCompanyName?: string;
        CardUniqueReference?: string;
        OriginalCurrencyCode?: string;
        ExternalTransactionId?: string;
        ResellerSourceAddress?: string;
        CardExpirationDate?: string;
        ServiceId?: string;
        RetrievalReferenceNumber?: string;
        AssignedMerchantUsers?: any[];
        AssignedResellerUsers?: any[];
        CardTypeId?: number;
        ResponseEventId?: string;
        ElectronicCommerceIndicator?: string;
        OrderServiceId?: number;
        ApplicationIdentifierTerminal?: string;
        IntegrationId?: string;
        CardProductCategoryId?: number;
        CardProductAccountTypeId?: number;
        DigitalWalletId?: number;
        DccSessionId?: string;
        DccMarkup?: number;
        DccDifferenceOverEcb?: number;
    };
    Created?: string;
    CorrelationId?: string;
    EventTypeId?: number;
    Delay?: number;
    RetryCount?: number;
    RetryDelayInSeconds?: number;
    MessageId?: string;
    RecipientId?: string;
    MessageTypeId?: number;
}, {
    Url?: string;
    EventData?: {
        Moto?: boolean;
        BinId?: number;
        IsDcc?: boolean;
        Ucaf?: string;
        Email?: string;
        Phone?: string;
        BankId?: string;
        Systemic?: boolean;
        BatchId?: string;
        Switching?: boolean;
        ParentId?: string;
        Amount?: number;
        ChannelId?: string;
        TerminalId?: number;
        MerchantId?: string;
        OrderCode?: number;
        ProductId?: string;
        StatusId?: string;
        FullName?: string;
        ResellerId?: string;
        DualMessage?: boolean;
        InsDate?: string;
        TotalFee?: number;
        CardToken?: string;
        CardNumber?: string;
        Descriptor?: string;
        TipAmount?: number;
        SourceCode?: string;
        SourceName?: string;
        Latitude?: number;
        Longitude?: number;
        CompanyName?: string;
        TransactionId?: string;
        CompanyTitle?: string;
        PanEntryMode?: string;
        ReferenceNumber?: number;
        ResponseCode?: string;
        CurrencyCode?: string;
        OrderCulture?: string;
        MerchantTrns?: string;
        CustomerTrns?: string;
        IsManualRefund?: boolean;
        TargetPersonId?: string;
        TargetWalletId?: string;
        AcquirerApproved?: boolean;
        LoyaltyTriggered?: boolean;
        TransactionTypeId?: number;
        AuthorizationId?: string;
        TotalInstallments?: number;
        CardCountryCode?: string;
        CardIssuingBank?: string;
        RedeemedAmount?: number;
        ClearanceDate?: string;
        ConversionRate?: number;
        CurrentInstallment?: number;
        OriginalAmount?: number;
        Tags?: string[];
        BillId?: string;
        ConnectedAccountId?: string;
        ResellerSourceCode?: string;
        ResellerSourceName?: string;
        MerchantCategoryCode?: number;
        ResellerCompanyName?: string;
        CardUniqueReference?: string;
        OriginalCurrencyCode?: string;
        ExternalTransactionId?: string;
        ResellerSourceAddress?: string;
        CardExpirationDate?: string;
        ServiceId?: string;
        RetrievalReferenceNumber?: string;
        AssignedMerchantUsers?: any[];
        AssignedResellerUsers?: any[];
        CardTypeId?: number;
        ResponseEventId?: string;
        ElectronicCommerceIndicator?: string;
        OrderServiceId?: number;
        ApplicationIdentifierTerminal?: string;
        IntegrationId?: string;
        CardProductCategoryId?: number;
        CardProductAccountTypeId?: number;
        DigitalWalletId?: number;
        DccSessionId?: string;
        DccMarkup?: number;
        DccDifferenceOverEcb?: number;
    };
    Created?: string;
    CorrelationId?: string;
    EventTypeId?: number;
    Delay?: number;
    RetryCount?: number;
    RetryDelayInSeconds?: number;
    MessageId?: string;
    RecipientId?: string;
    MessageTypeId?: number;
}>;
export type VivaWebhookEvent = z.infer<typeof vivaWebhookEventSchema>;
export type VivaTransactionEventData = z.infer<typeof vivaTransactionEventDataSchema>;
export interface VivaWebhookVerificationResponse {
    Key: string;
}
export interface VivaWebhookProcessingResult {
    processed: boolean;
    action?: string;
    transactionId?: string;
    error?: string;
}
declare class VivaWebhookService {
    private readonly merchantId;
    private readonly apiKey;
    private readonly environment;
    private readonly apiUrl;
    constructor();
    /**
     * Generate webhook verification key from Viva
     * This key is used to verify webhook authenticity
     */
    generateWebhookVerificationKey(): Promise<VivaWebhookVerificationResponse>;
    /**
     * Validate webhook event structure
     */
    validateWebhookEvent(payload: any): VivaWebhookEvent;
    /**
     * Verify webhook authenticity (placeholder for future implementation)
     * Viva doesn't provide signature verification in their documentation,
     * but we can implement IP whitelisting and other security measures
     */
    verifyWebhookAuthenticity(payload: string, headers: Record<string, any>): boolean;
    /**
     * Get event type description
     */
    getEventTypeDescription(eventTypeId: number): string;
}
export declare const vivaWebhookService: VivaWebhookService;
export {};
//# sourceMappingURL=vivaWebhookService.d.ts.map