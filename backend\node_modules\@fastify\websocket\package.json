{"name": "@fastify/websocket", "version": "11.2.0", "description": "basic websocket support for fastify", "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "scripts": {"lint": "eslint", "lint:fix": "eslint --fix", "test": "npm run test:unit && npm run test:typescript", "test:unit": "c8 --100 node --test", "test:typescript": "tsd"}, "repository": {"type": "git", "url": "git+https://github.com/fastify/fastify-websocket.git"}, "keywords": ["fastify", "websocket"], "author": "<PERSON> <<EMAIL>>", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/fdawgs"}], "license": "MIT", "bugs": {"url": "https://github.com/fastify/fastify-websocket/issues"}, "homepage": "https://github.com/fastify/fastify-websocket#readme", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "devDependencies": {"@fastify/pre-commit": "^2.1.0", "@fastify/type-provider-typebox": "^5.0.0", "@types/node": "^24.0.9", "@types/ws": "^8.5.10", "c8": "^10.1.3", "eslint": "^9.17.0", "fastify": "^5.0.0", "fastify-tsconfig": "^3.0.0", "neostandard": "^0.12.0", "split2": "^4.2.0", "tsd": "^0.32.0"}, "dependencies": {"duplexify": "^4.1.3", "fastify-plugin": "^5.0.0", "ws": "^8.16.0"}, "publishConfig": {"access": "public"}, "pre-commit": ["lint", "test"]}