"use strict";
/**
 * Viva Wallet Webhook Routes
 *
 * Handles Viva Wallet webhook events for payment notifications
 * Processes payment confirmations, failures, and other transaction events
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = vivaWebhookRoutes;
const vivaWebhookService_1 = require("../services/vivaWebhookService");
const SystemLog_1 = require("../models/SystemLog");
const Transaction_mongo_1 = __importDefault(require("../models/Transaction.mongo"));
const logger_1 = require("../config/logger");
const env_1 = require("../config/env");
const websocketService_1 = require("../services/websocketService");
const receiptService_1 = require("../services/receiptService");
const vivaWebhookLogger = (0, logger_1.createChildLogger)({ module: 'viva-webhook' });
async function vivaWebhookRoutes(fastify) {
    /**
     * Viva Wallet Webhook Endpoint
     * Handles all Viva Wallet webhook events
     */
    fastify.post('/viva/webhook', async (request, reply) => {
        try {
            const rawBody = JSON.stringify(request.body);
            const headers = request.headers;
            vivaWebhookLogger.info('Received Viva webhook', {
                headers: Object.keys(headers),
                bodySize: rawBody.length
            });
            // Verify webhook authenticity
            const isValidWebhook = vivaWebhookService_1.vivaWebhookService.verifyWebhookAuthenticity(rawBody, headers);
            if (!isValidWebhook) {
                vivaWebhookLogger.warn('Invalid Viva webhook signature or source');
                return reply.status(400).send({
                    success: false,
                    error: 'Invalid webhook signature or source'
                });
            }
            // Validate and parse webhook event
            const webhookEvent = vivaWebhookService_1.vivaWebhookService.validateWebhookEvent(request.body);
            // Log webhook event
            await logWebhookEvent(webhookEvent);
            // Handle the event
            const result = await handleVivaWebhookEvent(webhookEvent);
            vivaWebhookLogger.info('Viva webhook processed successfully', {
                eventType: webhookEvent.EventTypeId,
                eventDescription: vivaWebhookService_1.vivaWebhookService.getEventTypeDescription(webhookEvent.EventTypeId),
                transactionId: webhookEvent.EventData.TransactionId,
                orderCode: webhookEvent.EventData.OrderCode,
                result
            });
            // Return 2xx status as required by Viva
            reply.status(200).send({
                success: true,
                eventType: webhookEvent.EventTypeId,
                eventDescription: vivaWebhookService_1.vivaWebhookService.getEventTypeDescription(webhookEvent.EventTypeId),
                transactionId: webhookEvent.EventData.TransactionId,
                processed: result.processed
            });
        }
        catch (error) {
            vivaWebhookLogger.error('Viva webhook processing failed', error);
            // Return 500 status to trigger Viva's retry mechanism
            reply.status(500).send({
                success: false,
                error: error instanceof Error ? error.message : 'Webhook processing failed'
            });
        }
    });
    /**
     * Webhook verification endpoint
     * Returns the webhook verification key for Viva webhook setup
     */
    fastify.get('/viva/webhook/verify', async (request, reply) => {
        try {
            // Check if we have a stored verification key
            if (env_1.env.VIVA_WEBHOOK_VERIFICATION_KEY && env_1.env.VIVA_WEBHOOK_VERIFICATION_KEY !== 'your_viva_webhook_verification_key') {
                vivaWebhookLogger.info('Returning stored webhook verification key');
                return reply.send({
                    Key: env_1.env.VIVA_WEBHOOK_VERIFICATION_KEY
                });
            }
            // Generate new verification key from Viva
            const verificationResponse = await vivaWebhookService_1.vivaWebhookService.generateWebhookVerificationKey();
            vivaWebhookLogger.info('Generated new webhook verification key', {
                keyLength: verificationResponse.Key?.length || 0
            });
            reply.send(verificationResponse);
        }
        catch (error) {
            vivaWebhookLogger.error('Failed to get webhook verification key', error);
            reply.status(500).send({
                success: false,
                error: 'Failed to get webhook verification key'
            });
        }
    });
    /**
     * Webhook health check
     */
    fastify.get('/viva/webhook/health', async (_request, reply) => {
        reply.send({
            success: true,
            message: 'Viva webhook endpoint is healthy',
            timestamp: new Date().toISOString(),
            environment: env_1.env.VIVA_ENVIRONMENT
        });
    });
}
/**
 * Log webhook event to system logs
 */
async function logWebhookEvent(event) {
    try {
        await SystemLog_1.SystemLog.create({
            level: 'info',
            message: 'Viva webhook event received',
            metadata: {
                eventType: event.EventTypeId,
                eventDescription: vivaWebhookService_1.vivaWebhookService.getEventTypeDescription(event.EventTypeId),
                transactionId: event.EventData.TransactionId,
                orderCode: event.EventData.OrderCode,
                amount: event.EventData.Amount,
                statusId: event.EventData.StatusId,
                merchantId: event.EventData.MerchantId,
                correlationId: event.CorrelationId,
                messageId: event.MessageId,
                retryCount: event.RetryCount,
                created: event.Created,
            },
            timestamp: new Date(),
        });
    }
    catch (error) {
        vivaWebhookLogger.error('Failed to log webhook event', error);
    }
}
/**
 * Handle different Viva webhook events
 */
async function handleVivaWebhookEvent(event) {
    try {
        switch (event.EventTypeId) {
            case vivaWebhookService_1.VIVA_WEBHOOK_EVENTS.TRANSACTION_PAYMENT_CREATED:
                return await handleTransactionPaymentCreated(event);
            case vivaWebhookService_1.VIVA_WEBHOOK_EVENTS.TRANSACTION_FAILED:
                return await handleTransactionFailed(event);
            case vivaWebhookService_1.VIVA_WEBHOOK_EVENTS.TRANSACTION_REVERSAL_CREATED:
                return await handleTransactionReversal(event);
            case vivaWebhookService_1.VIVA_WEBHOOK_EVENTS.TRANSACTION_POS_ECR_SESSION_CREATED:
                return await handlePosEcrSessionCreated(event);
            case vivaWebhookService_1.VIVA_WEBHOOK_EVENTS.TRANSACTION_POS_ECR_SESSION_FAILED:
                return await handlePosEcrSessionFailed(event);
            default:
                vivaWebhookLogger.info(`Unhandled Viva event type: ${event.EventTypeId} - ${vivaWebhookService_1.vivaWebhookService.getEventTypeDescription(event.EventTypeId)}`);
                return { processed: false };
        }
    }
    catch (error) {
        vivaWebhookLogger.error('Error handling Viva webhook event', error);
        return {
            processed: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        };
    }
}
/**
 * Handle Transaction Payment Created event (EventTypeId: 1796)
 * This is triggered when a customer payment has been successful
 */
async function handleTransactionPaymentCreated(event) {
    try {
        const eventData = event.EventData;
        vivaWebhookLogger.info('Processing Transaction Payment Created', {
            transactionId: eventData.TransactionId,
            orderCode: eventData.OrderCode,
            amount: eventData.Amount,
            statusId: eventData.StatusId,
        });
        // Find the transaction by order code
        const transaction = await Transaction_mongo_1.default.findOne({
            'metadata.order_code': String(eventData.OrderCode),
            'metadata.payment_provider': 'viva',
        });
        if (!transaction) {
            vivaWebhookLogger.warn('Transaction not found for successful payment', {
                orderCode: eventData.OrderCode,
                transactionId: eventData.TransactionId,
            });
            return { processed: false, error: 'Transaction not found' };
        }
        // Update transaction status to success
        transaction.status = 'success';
        transaction.metadata = {
            ...transaction.metadata,
            viva_transaction_id: eventData.TransactionId,
            viva_status_id: eventData.StatusId,
            viva_response_code: eventData.ResponseCode,
            viva_authorization_id: eventData.AuthorizationId,
            viva_reference_number: eventData.ReferenceNumber,
            viva_card_number: eventData.CardNumber,
            viva_card_token: eventData.CardToken,
            viva_bank_id: eventData.BankId,
            viva_retrieval_reference_number: eventData.RetrievalReferenceNumber,
            webhook_processed_at: new Date().toISOString(),
        };
        transaction.updatedAt = new Date().toISOString();
        await transaction.save();
        vivaWebhookLogger.info('Transaction updated successfully', {
            transactionId: eventData.TransactionId,
            orderCode: eventData.OrderCode,
            dbTransactionId: transaction._id,
        });
        // Broadcast to WebSocket clients for real-time updates
        websocketService_1.websocketService.broadcastVivaWebhookEvent({
            eventType: vivaWebhookService_1.VIVA_WEBHOOK_EVENTS.TRANSACTION_PAYMENT_CREATED,
            eventDescription: 'Transaction Payment Created',
            transactionId: eventData.TransactionId,
            orderCode: eventData.OrderCode,
            amount: eventData.Amount,
            statusId: eventData.StatusId,
            processed: true,
        });
        // Broadcast transaction update
        websocketService_1.websocketService.broadcastTransactionUpdate(transaction);
        // Generate and broadcast receipt
        try {
            const customerReceipt = receiptService_1.ReceiptService.generateVivaCustomerReceipt(transaction, eventData);
            const merchantReceipt = receiptService_1.ReceiptService.generateVivaMerchantReceipt(transaction, eventData);
            // Broadcast receipt events
            websocketService_1.websocketService.broadcastReceiptEvent({
                transactionId: eventData.TransactionId,
                receiptType: 'customer',
                receiptContent: customerReceipt,
                format: 'text'
            });
            websocketService_1.websocketService.broadcastReceiptEvent({
                transactionId: eventData.TransactionId,
                receiptType: 'merchant',
                receiptContent: merchantReceipt,
                format: 'text'
            });
            vivaWebhookLogger.info('Receipts generated and broadcasted', {
                transactionId: eventData.TransactionId,
                orderCode: eventData.OrderCode,
            });
        }
        catch (receiptError) {
            vivaWebhookLogger.error('Failed to generate receipts', {
                transactionId: eventData.TransactionId,
                error: receiptError
            });
        }
        return {
            processed: true,
            action: 'transaction_updated_to_success',
            transactionId: eventData.TransactionId
        };
    }
    catch (error) {
        vivaWebhookLogger.error('Error processing Transaction Payment Created', error);
        return {
            processed: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        };
    }
}
/**
 * Handle Transaction Failed event (EventTypeId: 1798)
 */
async function handleTransactionFailed(event) {
    try {
        const eventData = event.EventData;
        vivaWebhookLogger.info('Processing Transaction Failed', {
            transactionId: eventData.TransactionId,
            orderCode: eventData.OrderCode,
            statusId: eventData.StatusId,
        });
        // Find and update transaction
        const transaction = await Transaction_mongo_1.default.findOne({
            'metadata.order_code': String(eventData.OrderCode),
            'metadata.payment_provider': 'viva',
        });
        if (transaction) {
            transaction.status = 'failed';
            transaction.metadata = {
                ...transaction.metadata,
                viva_transaction_id: eventData.TransactionId,
                viva_status_id: eventData.StatusId,
                viva_response_code: eventData.ResponseCode,
                webhook_processed_at: new Date().toISOString(),
            };
            transaction.updatedAt = new Date().toISOString();
            await transaction.save();
            // Broadcast to WebSocket clients
            websocketService_1.websocketService.broadcastVivaWebhookEvent({
                eventType: vivaWebhookService_1.VIVA_WEBHOOK_EVENTS.TRANSACTION_FAILED,
                eventDescription: 'Transaction Failed',
                transactionId: eventData.TransactionId,
                orderCode: eventData.OrderCode,
                amount: eventData.Amount || 0,
                statusId: eventData.StatusId,
                processed: true,
            });
            // Broadcast transaction update
            websocketService_1.websocketService.broadcastTransactionUpdate(transaction);
        }
        return {
            processed: true,
            action: 'transaction_updated_to_failed',
            transactionId: eventData.TransactionId
        };
    }
    catch (error) {
        vivaWebhookLogger.error('Error processing Transaction Failed', error);
        return { processed: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
}
/**
 * Handle Transaction Reversal Created event (EventTypeId: 1797)
 */
async function handleTransactionReversal(event) {
    try {
        const eventData = event.EventData;
        vivaWebhookLogger.info('Processing Transaction Reversal', {
            transactionId: eventData.TransactionId,
            orderCode: eventData.OrderCode,
        });
        // Find and update transaction
        const transaction = await Transaction_mongo_1.default.findOne({
            'metadata.order_code': String(eventData.OrderCode),
            'metadata.payment_provider': 'viva',
        });
        if (transaction) {
            transaction.status = 'refunded';
            transaction.metadata = {
                ...transaction.metadata,
                viva_reversal_transaction_id: eventData.TransactionId,
                viva_status_id: eventData.StatusId,
                webhook_processed_at: new Date().toISOString(),
            };
            transaction.updatedAt = new Date().toISOString();
            await transaction.save();
            // Broadcast to WebSocket clients
            websocketService_1.websocketService.broadcastVivaWebhookEvent({
                eventType: vivaWebhookService_1.VIVA_WEBHOOK_EVENTS.TRANSACTION_REVERSAL_CREATED,
                eventDescription: 'Transaction Reversal Created',
                transactionId: eventData.TransactionId,
                orderCode: eventData.OrderCode,
                amount: eventData.Amount || 0,
                statusId: eventData.StatusId,
                processed: true,
            });
            // Broadcast transaction update
            websocketService_1.websocketService.broadcastTransactionUpdate(transaction);
        }
        return {
            processed: true,
            action: 'transaction_refunded',
            transactionId: eventData.TransactionId
        };
    }
    catch (error) {
        vivaWebhookLogger.error('Error processing Transaction Reversal', error);
        return { processed: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
}
/**
 * Handle POS ECR Session Created event (EventTypeId: 1802)
 */
async function handlePosEcrSessionCreated(event) {
    vivaWebhookLogger.info('Processing POS ECR Session Created', {
        transactionId: event.EventData.TransactionId,
        orderCode: event.EventData.OrderCode,
    });
    // Handle ECR integration events
    return { processed: true, action: 'pos_ecr_session_created' };
}
/**
 * Handle POS ECR Session Failed event (EventTypeId: 1803)
 */
async function handlePosEcrSessionFailed(event) {
    vivaWebhookLogger.info('Processing POS ECR Session Failed', {
        transactionId: event.EventData.TransactionId,
        orderCode: event.EventData.OrderCode,
    });
    // Handle ECR integration events
    return { processed: true, action: 'pos_ecr_session_failed' };
}
//# sourceMappingURL=viva-webhook.routes.js.map