"use strict";
/**
 * Viva Wallet Webhook Service
 *
 * Handles Viva Wallet webhook verification and event processing
 * Supports webhook verification key generation and signature validation
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.vivaWebhookService = exports.VIVA_WEBHOOK_EVENTS = void 0;
const axios_1 = __importDefault(require("axios"));
const zod_1 = require("zod");
const env_1 = require("../config/env");
const logger_1 = require("../config/logger");
const vivaWebhookLogger = (0, logger_1.createChildLogger)({ module: 'viva-webhook' });
// Viva Webhook Event Types
exports.VIVA_WEBHOOK_EVENTS = {
    TRANSACTION_PAYMENT_CREATED: 1796,
    TRANSACTION_FAILED: 1798,
    TRANSACTION_PRICE_CALCULATED: 1799,
    TRANSACTION_REVERSAL_CREATED: 1797,
    ACCOUNT_TRANSACTION_CREATED: 2054,
    COMMAND_BANK_TRANSFER_CREATED: 768,
    COMMAND_BANK_TRANSFER_EXECUTED: 769,
    ACCOUNT_CONNECTED: 8193,
    ACCOUNT_VERIFICATION_STATUS_CHANGED: 8194,
    TRANSFER_CREATED: 8448,
    TRANSACTION_POS_ECR_SESSION_CREATED: 1802,
    TRANSACTION_POS_ECR_SESSION_FAILED: 1803,
};
// Validation schemas
const vivaTransactionEventDataSchema = zod_1.z.object({
    Moto: zod_1.z.boolean().optional(),
    BinId: zod_1.z.number().optional(),
    IsDcc: zod_1.z.boolean().optional(),
    Ucaf: zod_1.z.string().optional(),
    Email: zod_1.z.string().email().optional(),
    Phone: zod_1.z.string().optional(),
    BankId: zod_1.z.string().optional(),
    Systemic: zod_1.z.boolean().optional(),
    BatchId: zod_1.z.string().optional(),
    Switching: zod_1.z.boolean().optional(),
    ParentId: zod_1.z.string().nullable().optional(),
    Amount: zod_1.z.number(),
    ChannelId: zod_1.z.string().optional(),
    TerminalId: zod_1.z.number().optional(),
    MerchantId: zod_1.z.string(),
    OrderCode: zod_1.z.number(),
    ProductId: zod_1.z.string().nullable().optional(),
    StatusId: zod_1.z.string(),
    FullName: zod_1.z.string().optional(),
    ResellerId: zod_1.z.string().nullable().optional(),
    DualMessage: zod_1.z.boolean().optional(),
    InsDate: zod_1.z.string(),
    TotalFee: zod_1.z.number().optional(),
    CardToken: zod_1.z.string().optional(),
    CardNumber: zod_1.z.string().optional(),
    Descriptor: zod_1.z.string().nullable().optional(),
    TipAmount: zod_1.z.number().optional(),
    SourceCode: zod_1.z.string().optional(),
    SourceName: zod_1.z.string().optional(),
    Latitude: zod_1.z.number().nullable().optional(),
    Longitude: zod_1.z.number().nullable().optional(),
    CompanyName: zod_1.z.string().optional(),
    TransactionId: zod_1.z.string(),
    CompanyTitle: zod_1.z.string().optional(),
    PanEntryMode: zod_1.z.string().optional(),
    ReferenceNumber: zod_1.z.number().optional(),
    ResponseCode: zod_1.z.string().optional(),
    CurrencyCode: zod_1.z.string().optional(),
    OrderCulture: zod_1.z.string().optional(),
    MerchantTrns: zod_1.z.string().optional(),
    CustomerTrns: zod_1.z.string().optional(),
    IsManualRefund: zod_1.z.boolean().optional(),
    TargetPersonId: zod_1.z.string().nullable().optional(),
    TargetWalletId: zod_1.z.string().nullable().optional(),
    AcquirerApproved: zod_1.z.boolean().optional(),
    LoyaltyTriggered: zod_1.z.boolean().optional(),
    TransactionTypeId: zod_1.z.number().optional(),
    AuthorizationId: zod_1.z.string().optional(),
    TotalInstallments: zod_1.z.number().optional(),
    CardCountryCode: zod_1.z.string().optional(),
    CardIssuingBank: zod_1.z.string().optional(),
    RedeemedAmount: zod_1.z.number().optional(),
    ClearanceDate: zod_1.z.string().nullable().optional(),
    ConversionRate: zod_1.z.number().optional(),
    CurrentInstallment: zod_1.z.number().optional(),
    OriginalAmount: zod_1.z.number().optional(),
    Tags: zod_1.z.array(zod_1.z.string()).optional(),
    BillId: zod_1.z.string().nullable().optional(),
    ConnectedAccountId: zod_1.z.string().nullable().optional(),
    ResellerSourceCode: zod_1.z.string().nullable().optional(),
    ResellerSourceName: zod_1.z.string().nullable().optional(),
    MerchantCategoryCode: zod_1.z.number().optional(),
    ResellerCompanyName: zod_1.z.string().nullable().optional(),
    CardUniqueReference: zod_1.z.string().optional(),
    OriginalCurrencyCode: zod_1.z.string().optional(),
    ExternalTransactionId: zod_1.z.string().nullable().optional(),
    ResellerSourceAddress: zod_1.z.string().nullable().optional(),
    CardExpirationDate: zod_1.z.string().optional(),
    ServiceId: zod_1.z.string().nullable().optional(),
    RetrievalReferenceNumber: zod_1.z.string().optional(),
    AssignedMerchantUsers: zod_1.z.array(zod_1.z.any()).optional(),
    AssignedResellerUsers: zod_1.z.array(zod_1.z.any()).optional(),
    CardTypeId: zod_1.z.number().optional(),
    ResponseEventId: zod_1.z.string().nullable().optional(),
    ElectronicCommerceIndicator: zod_1.z.string().optional(),
    OrderServiceId: zod_1.z.number().optional(),
    ApplicationIdentifierTerminal: zod_1.z.string().nullable().optional(),
    IntegrationId: zod_1.z.string().nullable().optional(),
    CardProductCategoryId: zod_1.z.number().optional(),
    CardProductAccountTypeId: zod_1.z.number().optional(),
    DigitalWalletId: zod_1.z.number().optional(),
    DccSessionId: zod_1.z.string().nullable().optional(),
    DccMarkup: zod_1.z.number().nullable().optional(),
    DccDifferenceOverEcb: zod_1.z.number().nullable().optional(),
});
const vivaWebhookEventSchema = zod_1.z.object({
    Url: zod_1.z.string().url(),
    EventData: vivaTransactionEventDataSchema,
    Created: zod_1.z.string(),
    CorrelationId: zod_1.z.string(),
    EventTypeId: zod_1.z.number(),
    Delay: zod_1.z.number().nullable().optional(),
    RetryCount: zod_1.z.number(),
    RetryDelayInSeconds: zod_1.z.number().nullable().optional(),
    MessageId: zod_1.z.string(),
    RecipientId: zod_1.z.string(),
    MessageTypeId: zod_1.z.number(),
});
class VivaWebhookService {
    constructor() {
        this.merchantId = env_1.env.VIVA_MERCHANT_ID;
        this.apiKey = env_1.env.VIVA_API_KEY;
        this.environment = env_1.env.VIVA_ENVIRONMENT;
        this.apiUrl = env_1.env.VIVA_API_URL || (this.environment === 'production'
            ? 'https://www.vivapayments.com'
            : 'https://demo.vivapayments.com');
        vivaWebhookLogger.info('Viva Webhook Service initialized', {
            environment: this.environment,
            merchantId: this.merchantId,
            apiUrl: this.apiUrl,
        });
    }
    /**
     * Generate webhook verification key from Viva
     * This key is used to verify webhook authenticity
     */
    async generateWebhookVerificationKey() {
        try {
            vivaWebhookLogger.info('Generating Viva webhook verification key');
            const credentials = Buffer.from(`${this.merchantId}:${this.apiKey}`).toString('base64');
            const response = await axios_1.default.get(`${this.apiUrl}/api/messages/config/token`, {
                headers: {
                    'Authorization': `Basic ${credentials}`,
                    'Content-Type': 'application/json',
                },
                timeout: 10000,
            });
            vivaWebhookLogger.info('Webhook verification key generated successfully', {
                keyLength: response.data.Key?.length || 0,
            });
            return response.data;
        }
        catch (error) {
            vivaWebhookLogger.error('Failed to generate webhook verification key', error);
            throw new Error('Failed to generate webhook verification key');
        }
    }
    /**
     * Validate webhook event structure
     */
    validateWebhookEvent(payload) {
        try {
            return vivaWebhookEventSchema.parse(payload);
        }
        catch (error) {
            vivaWebhookLogger.error('Invalid webhook event structure', { error, payload });
            throw new Error('Invalid webhook event structure');
        }
    }
    /**
     * Verify webhook authenticity (placeholder for future implementation)
     * Viva doesn't provide signature verification in their documentation,
     * but we can implement IP whitelisting and other security measures
     */
    verifyWebhookAuthenticity(payload, headers) {
        try {
            // For now, we'll implement basic verification
            // In production, you should implement IP whitelisting based on Viva's IP ranges
            // Check if the request comes from allowed IPs (implement IP whitelisting)
            const allowedIPs = this.environment === 'production'
                ? [
                    '*************', '************', '************',
                    '***********', '**************/28', '**************/28',
                    '***********', '***********', '***********/28'
                ]
                : [
                    '************', '***********', '************',
                    '************', '************', '*************',
                    '*************', '************'
                ];
            // For development, we'll allow all requests
            // In production, implement proper IP validation
            if (this.environment === 'demo') {
                return true;
            }
            // TODO: Implement IP whitelisting validation
            return true;
        }
        catch (error) {
            vivaWebhookLogger.error('Webhook verification failed', error);
            return false;
        }
    }
    /**
     * Get event type description
     */
    getEventTypeDescription(eventTypeId) {
        const eventDescriptions = {
            [exports.VIVA_WEBHOOK_EVENTS.TRANSACTION_PAYMENT_CREATED]: 'Transaction Payment Created',
            [exports.VIVA_WEBHOOK_EVENTS.TRANSACTION_FAILED]: 'Transaction Failed',
            [exports.VIVA_WEBHOOK_EVENTS.TRANSACTION_PRICE_CALCULATED]: 'Transaction Price Calculated',
            [exports.VIVA_WEBHOOK_EVENTS.TRANSACTION_REVERSAL_CREATED]: 'Transaction Reversal Created',
            [exports.VIVA_WEBHOOK_EVENTS.ACCOUNT_TRANSACTION_CREATED]: 'Account Transaction Created',
            [exports.VIVA_WEBHOOK_EVENTS.COMMAND_BANK_TRANSFER_CREATED]: 'Command Bank Transfer Created',
            [exports.VIVA_WEBHOOK_EVENTS.COMMAND_BANK_TRANSFER_EXECUTED]: 'Command Bank Transfer Executed',
            [exports.VIVA_WEBHOOK_EVENTS.ACCOUNT_CONNECTED]: 'Account Connected',
            [exports.VIVA_WEBHOOK_EVENTS.ACCOUNT_VERIFICATION_STATUS_CHANGED]: 'Account Verification Status Changed',
            [exports.VIVA_WEBHOOK_EVENTS.TRANSFER_CREATED]: 'Transfer Created',
            [exports.VIVA_WEBHOOK_EVENTS.TRANSACTION_POS_ECR_SESSION_CREATED]: 'Transaction POS ECR Session Created',
            [exports.VIVA_WEBHOOK_EVENTS.TRANSACTION_POS_ECR_SESSION_FAILED]: 'Transaction POS ECR Session Failed',
        };
        return eventDescriptions[eventTypeId] || `Unknown Event Type: ${eventTypeId}`;
    }
}
exports.vivaWebhookService = new VivaWebhookService();
//# sourceMappingURL=vivaWebhookService.js.map