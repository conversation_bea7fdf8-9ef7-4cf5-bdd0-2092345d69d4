import { ITransaction } from '../models/Transaction.mongo';
import { VivaTransactionEventData } from './vivaWebhookService';
export interface MerchantInfo {
    name: string;
    address: string;
    city: string;
    state: string;
    zipCode: string;
    phone: string;
    email?: string;
    website?: string;
    taxId?: string;
}
export interface ReceiptData {
    transactionId: string;
    amount: number;
    status: string;
    timestamp: Date;
    paymentMethod?: string;
    cardLast4?: string;
    authCode?: string;
    protocolCode?: string;
    merchantInfo: MerchantInfo;
    customerCopy: boolean;
}
export interface PrinterConfig {
    width: number;
    paperType: 'thermal' | 'impact';
    encoding: 'utf8' | 'ascii';
}
export declare class ReceiptService {
    private static defaultMerchantInfo;
    private static printerConfig;
    static generateReceipt(transaction: ITransaction, customerCopy?: boolean): string;
    static generateMerchantReceipt(transaction: ITransaction): string;
    static generateCustomerReceipt(transaction: ITransaction): string;
    /**
     * Generate receipt specifically for Viva Wallet transactions
     * Uses Viva-specific transaction data from webhook events
     */
    static generateVivaReceipt(transaction: ITransaction, vivaData: VivaTransactionEventData, customerCopy?: boolean): string;
    /**
     * Generate customer receipt for Viva transactions
     */
    static generateVivaCustomerReceipt(transaction: ITransaction, vivaData: VivaTransactionEventData): string;
    /**
     * Generate merchant receipt for Viva transactions
     */
    static generateVivaMerchantReceipt(transaction: ITransaction, vivaData: VivaTransactionEventData): string;
    private static formatReceipt;
    private static centerText;
    private static leftRightText;
    private static formatDate;
    private static formatTime;
    private static generateAuthCode;
    /**
     * Format receipt specifically for Viva Wallet transactions
     */
    private static formatVivaReceipt;
    /**
     * Get payment method description for Viva transactions
     */
    private static getVivaPaymentMethod;
    /**
     * Extract last 4 digits from masked card number
     */
    private static extractCardLast4;
    /**
     * Wrap text to fit within specified width
     */
    private static wrapText;
}
export declare const generateReceipt: (txn: any) => string;
//# sourceMappingURL=receiptService.d.ts.map