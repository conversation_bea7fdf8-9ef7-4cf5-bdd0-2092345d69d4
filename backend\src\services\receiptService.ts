import { ITransaction } from '../models/Transaction.mongo';
import { logger } from '../config/logger';
import { VivaTransactionEventData } from './vivaWebhookService';

export interface MerchantInfo {
  name: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  phone: string;
  email?: string;
  website?: string;
  taxId?: string;
}

export interface ReceiptData {
  transactionId: string;
  amount: number;
  status: string;
  timestamp: Date;
  paymentMethod?: string;
  cardLast4?: string;
  authCode?: string;
  protocolCode?: string;
  merchantInfo: MerchantInfo;
  customerCopy: boolean;
}

export interface PrinterConfig {
  width: number; // characters per line
  paperType: 'thermal' | 'impact';
  encoding: 'utf8' | 'ascii';
}

export class ReceiptService {
  private static defaultMerchantInfo: MerchantInfo = {
    name: 'Demo Store',
    address: '123 Main Street',
    city: 'Anytown',
    state: 'ST',
    zipCode: '12345',
    phone: '(*************',
    email: '<EMAIL>',
    website: 'www.demostore.com',
    taxId: 'TAX123456789'
  };

  private static printerConfig: PrinterConfig = {
    width: 32,
    paperType: 'thermal',
    encoding: 'utf8'
  };

  static generateReceipt(transaction: ITransaction, customerCopy: boolean = true): string {
    try {
      const receiptData: ReceiptData = {
        transactionId: transaction._id?.toString() || '',
        amount: transaction.amount,
        status: transaction.status,
        timestamp: new Date(transaction.createdAt || new Date()),
        paymentMethod: 'Credit Card',
        cardLast4: '****',
        authCode: this.generateAuthCode(),
        protocolCode: transaction.protocolCode,
        merchantInfo: this.defaultMerchantInfo,
        customerCopy
      };

      return this.formatReceipt(receiptData);
    } catch (error) {
      logger.error('Error generating receipt:', error);
      throw new Error('Failed to generate receipt');
    }
  }

  static generateMerchantReceipt(transaction: ITransaction): string {
    return this.generateReceipt(transaction, false);
  }

  static generateCustomerReceipt(transaction: ITransaction): string {
    return this.generateReceipt(transaction, true);
  }

  /**
   * Generate receipt specifically for Viva Wallet transactions
   * Uses Viva-specific transaction data from webhook events
   */
  static generateVivaReceipt(transaction: ITransaction, vivaData: VivaTransactionEventData, customerCopy: boolean = true): string {
    try {
      const receiptData: ReceiptData = {
        transactionId: vivaData.TransactionId || transaction._id?.toString() || '',
        amount: vivaData.Amount || transaction.amount,
        status: transaction.status,
        timestamp: new Date(vivaData.InsDate || transaction.createdAt || new Date()),
        paymentMethod: this.getVivaPaymentMethod(vivaData),
        cardLast4: this.extractCardLast4(vivaData.CardNumber),
        authCode: vivaData.AuthorizationId || this.generateAuthCode(),
        protocolCode: transaction.protocolCode,
        merchantInfo: this.defaultMerchantInfo,
        customerCopy
      };

      return this.formatVivaReceipt(receiptData, vivaData);
    } catch (error) {
      logger.error('Error generating Viva receipt:', error);
      throw new Error('Failed to generate Viva receipt');
    }
  }

  /**
   * Generate customer receipt for Viva transactions
   */
  static generateVivaCustomerReceipt(transaction: ITransaction, vivaData: VivaTransactionEventData): string {
    return this.generateVivaReceipt(transaction, vivaData, true);
  }

  /**
   * Generate merchant receipt for Viva transactions
   */
  static generateVivaMerchantReceipt(transaction: ITransaction, vivaData: VivaTransactionEventData): string {
    return this.generateVivaReceipt(transaction, vivaData, false);
  }

  private static formatReceipt(data: ReceiptData): string {
    const width = this.printerConfig.width;
    const lines: string[] = [];

    // Header
    lines.push(this.centerText('', width, '='));
    lines.push(this.centerText(data.merchantInfo.name.toUpperCase(), width));
    lines.push(this.centerText(data.merchantInfo.address, width));
    lines.push(this.centerText(`${data.merchantInfo.city}, ${data.merchantInfo.state} ${data.merchantInfo.zipCode}`, width));
    lines.push(this.centerText(data.merchantInfo.phone, width));
    if (data.merchantInfo.website) {
      lines.push(this.centerText(data.merchantInfo.website, width));
    }
    lines.push(this.centerText('', width, '='));
    lines.push('');

    // Transaction details
    lines.push(this.leftRightText('Date:', this.formatDate(data.timestamp), width));
    lines.push(this.leftRightText('Time:', this.formatTime(data.timestamp), width));
    lines.push(this.leftRightText('Trans ID:', data.transactionId.slice(-8), width));
    if (data.authCode) {
      lines.push(this.leftRightText('Auth Code:', data.authCode, width));
    }
    if (data.protocolCode) {
      lines.push(this.leftRightText('Protocol:', data.protocolCode, width));
    }
    lines.push('');

    // Payment details
    lines.push(this.centerText('PAYMENT DETAILS', width, '-'));
    lines.push(this.leftRightText('Payment Method:', data.paymentMethod || 'Credit Card', width));
    if (data.cardLast4) {
      lines.push(this.leftRightText('Card:', `****${data.cardLast4}`, width));
    }
    lines.push('');

    // Amount
    lines.push(this.centerText('', width, '-'));
    lines.push(this.leftRightText('AMOUNT:', `$${(data.amount / 100).toFixed(2)}`, width));
    lines.push(this.centerText('', width, '-'));
    lines.push('');

    // Status
    const statusText = data.status === 'success' ? 'APPROVED' : 'DECLINED';
    lines.push(this.centerText(`*** ${statusText} ***`, width));
    lines.push('');

    // Copy type
    const copyType = data.customerCopy ? 'CUSTOMER COPY' : 'MERCHANT COPY';
    lines.push(this.centerText(copyType, width));
    lines.push('');

    // Footer
    if (data.status === 'success') {
      lines.push(this.centerText('Thank you for your business!', width));
      lines.push(this.centerText('Please keep this receipt', width));
      lines.push(this.centerText('for your records', width));
    } else {
      lines.push(this.centerText('Transaction declined', width));
      lines.push(this.centerText('Please try another payment method', width));
    }
    lines.push('');
    lines.push(this.centerText('', width, '='));
    lines.push('');

    return lines.join('\n');
  }

  private static centerText(text: string, width: number, fillChar: string = ' '): string {
    if (text.length >= width) return text.substring(0, width);
    const padding = width - text.length;
    const leftPad = Math.floor(padding / 2);
    const rightPad = padding - leftPad;
    return fillChar.repeat(leftPad) + text + fillChar.repeat(rightPad);
  }

  private static leftRightText(left: string, right: string, width: number): string {
    const totalTextLength = left.length + right.length;
    if (totalTextLength >= width) {
      return (left + right).substring(0, width);
    }
    const spaces = width - totalTextLength;
    return left + ' '.repeat(spaces) + right;
  }

  private static formatDate(date: Date): string {
    return date.toLocaleDateString('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: 'numeric'
    });
  }

  private static formatTime(date: Date): string {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true
    });
  }

  private static generateAuthCode(): string {
    return Math.random().toString(36).substring(2, 8).toUpperCase();
  }

  /**
   * Format receipt specifically for Viva Wallet transactions
   */
  private static formatVivaReceipt(data: ReceiptData, vivaData: VivaTransactionEventData): string {
    const width = this.printerConfig.width;
    const lines: string[] = [];

    // Header
    lines.push(this.centerText('', width, '='));
    lines.push(this.centerText(data.merchantInfo.name.toUpperCase(), width));
    lines.push(this.centerText('VIVA WALLET PAYMENT', width));
    lines.push(this.centerText('', width, '='));
    lines.push('');

    // Merchant Info
    lines.push(data.merchantInfo.address);
    lines.push(`${data.merchantInfo.city}, ${data.merchantInfo.state} ${data.merchantInfo.zipCode}`);
    if (data.merchantInfo.phone) {
      lines.push(`Phone: ${data.merchantInfo.phone}`);
    }
    if (data.merchantInfo.email) {
      lines.push(`Email: ${data.merchantInfo.email}`);
    }
    lines.push('');

    // Transaction Details
    lines.push(this.centerText('TRANSACTION DETAILS', width, '-'));
    lines.push(`Date/Time: ${data.timestamp.toLocaleString()}`);
    lines.push(`Transaction ID: ${data.transactionId}`);
    if (vivaData.OrderCode) {
      lines.push(`Order Code: ${vivaData.OrderCode}`);
    }
    if (vivaData.ReferenceNumber) {
      lines.push(`Reference: ${vivaData.ReferenceNumber}`);
    }
    lines.push('');

    // Payment Details
    lines.push(this.centerText('PAYMENT DETAILS', width, '-'));
    lines.push(`Payment Method: ${data.paymentMethod}`);
    if (data.cardLast4) {
      lines.push(`Card: ****${data.cardLast4}`);
    }
    if (vivaData.CardIssuingBank) {
      lines.push(`Issuing Bank: ${vivaData.CardIssuingBank}`);
    }
    if (data.authCode) {
      lines.push(`Auth Code: ${data.authCode}`);
    }
    if (vivaData.ResponseCode) {
      lines.push(`Response Code: ${vivaData.ResponseCode}`);
    }
    lines.push('');

    // Amount
    lines.push(this.centerText('AMOUNT', width, '-'));
    const currency = vivaData.CurrencyCode === '978' ? 'EUR' :
                    vivaData.CurrencyCode === '826' ? 'GBP' :
                    vivaData.CurrencyCode === '840' ? 'USD' : 'EUR';
    const amount = (data.amount / 100).toFixed(2); // Convert from cents
    lines.push(this.rightAlign(`TOTAL: ${currency} ${amount}`, width));

    if (vivaData.TipAmount && vivaData.TipAmount > 0) {
      const tipAmount = (vivaData.TipAmount / 100).toFixed(2);
      lines.push(this.rightAlign(`Tip: ${currency} ${tipAmount}`, width));
    }

    if (vivaData.TotalFee && vivaData.TotalFee > 0) {
      const feeAmount = (vivaData.TotalFee / 100).toFixed(2);
      lines.push(this.rightAlign(`Fee: ${currency} ${feeAmount}`, width));
    }
    lines.push('');

    // Status
    lines.push(this.centerText('STATUS', width, '-'));
    lines.push(this.centerText(data.status.toUpperCase(), width));
    lines.push('');

    // Viva-specific information
    if (vivaData.MerchantTrns) {
      lines.push(this.centerText('DESCRIPTION', width, '-'));
      lines.push(this.wrapText(vivaData.MerchantTrns, width));
      lines.push('');
    }

    // Copy type
    lines.push(this.centerText(data.customerCopy ? 'CUSTOMER COPY' : 'MERCHANT COPY', width, '-'));
    lines.push('');

    // Footer
    lines.push(this.centerText('Thank you for your business!', width));
    if (data.merchantInfo.website) {
      lines.push(this.centerText(data.merchantInfo.website, width));
    }
    lines.push('');
    lines.push(this.centerText('Powered by Viva Wallet', width));
    lines.push(this.centerText('', width, '='));

    return lines.join('\n');
  }

  /**
   * Get payment method description for Viva transactions
   */
  private static getVivaPaymentMethod(vivaData: VivaTransactionEventData): string {
    if (vivaData.CardNumber) {
      const cardType = vivaData.BankId || 'Card';
      return `${cardType} Payment`;
    }
    if (vivaData.DigitalWalletId) {
      return 'Digital Wallet';
    }
    return 'Viva Wallet Payment';
  }

  /**
   * Extract last 4 digits from masked card number
   */
  private static extractCardLast4(cardNumber?: string): string {
    if (!cardNumber) return '';

    // Extract last 4 digits from formats like "414746XXXXXX0133"
    const match = cardNumber.match(/\d{4}$/);
    return match ? match[0] : '';
  }

  /**
   * Wrap text to fit within specified width
   */
  private static wrapText(text: string, width: number): string {
    if (text.length <= width) return text;

    const words = text.split(' ');
    const lines: string[] = [];
    let currentLine = '';

    for (const word of words) {
      if ((currentLine + word).length <= width) {
        currentLine += (currentLine ? ' ' : '') + word;
      } else {
        if (currentLine) lines.push(currentLine);
        currentLine = word;
      }
    }

    if (currentLine) lines.push(currentLine);
    return lines.join('\n');
  }
}

// Legacy function for backward compatibility
export const generateReceipt = (txn: any) => {
  return ReceiptService.generateReceipt(txn);
};
