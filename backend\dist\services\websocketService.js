"use strict";
/**
 * WebSocket Service
 *
 * Handles real-time communication between backend and frontend
 * Broadcasts payment events, transaction updates, and other real-time data
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.websocketService = void 0;
const ws_1 = require("ws");
const logger_1 = require("../config/logger");
const wsLogger = (0, logger_1.createChildLogger)({ module: 'websocket' });
class WebSocketService {
    constructor() {
        this.wss = null;
        this.clients = new Set();
    }
    /**
     * Initialize WebSocket server
     */
    initialize(server) {
        try {
            this.wss = new ws_1.WebSocketServer({
                server,
                path: '/ws',
                perMessageDeflate: false
            });
            this.wss.on('connection', (ws, request) => {
                const clientId = this.generateClientId();
                wsLogger.info('WebSocket client connected', {
                    clientId,
                    origin: request.headers.origin,
                    userAgent: request.headers['user-agent']
                });
                // Add client to active connections
                this.clients.add(ws);
                // Set up client event handlers
                this.setupClientHandlers(ws, clientId);
                // Send welcome message
                this.sendToClient(ws, {
                    type: 'connection',
                    data: {
                        clientId,
                        message: 'Connected to payment system WebSocket',
                        timestamp: new Date().toISOString()
                    },
                    timestamp: new Date().toISOString()
                });
            });
            this.wss.on('error', (error) => {
                wsLogger.error('WebSocket server error', error);
            });
            wsLogger.info('WebSocket server initialized successfully');
        }
        catch (error) {
            wsLogger.error('Failed to initialize WebSocket server', error);
            throw error;
        }
    }
    /**
     * Set up event handlers for a WebSocket client
     */
    setupClientHandlers(ws, clientId) {
        ws.on('message', (data) => {
            try {
                const message = JSON.parse(data.toString());
                wsLogger.debug('Received message from client', { clientId, message });
                // Handle different message types
                this.handleClientMessage(ws, clientId, message);
            }
            catch (error) {
                wsLogger.error('Error parsing client message', { clientId, error });
            }
        });
        ws.on('close', (code, reason) => {
            wsLogger.info('WebSocket client disconnected', {
                clientId,
                code,
                reason: reason.toString()
            });
            this.clients.delete(ws);
        });
        ws.on('error', (error) => {
            wsLogger.error('WebSocket client error', { clientId, error });
            this.clients.delete(ws);
        });
        // Set up ping/pong for connection health
        ws.on('pong', () => {
            wsLogger.debug('Received pong from client', { clientId });
        });
    }
    /**
     * Handle messages from WebSocket clients
     */
    handleClientMessage(ws, clientId, message) {
        switch (message.type) {
            case 'ping':
                this.sendToClient(ws, {
                    type: 'pong',
                    data: { timestamp: new Date().toISOString() },
                    timestamp: new Date().toISOString()
                });
                break;
            case 'subscribe':
                // Handle subscription to specific events
                wsLogger.info('Client subscribed to events', {
                    clientId,
                    events: message.data?.events
                });
                break;
            case 'unsubscribe':
                // Handle unsubscription from events
                wsLogger.info('Client unsubscribed from events', {
                    clientId,
                    events: message.data?.events
                });
                break;
            default:
                wsLogger.warn('Unknown message type from client', { clientId, type: message.type });
        }
    }
    /**
     * Send message to a specific client
     */
    sendToClient(ws, message) {
        try {
            if (ws.readyState === ws_1.WebSocket.OPEN) {
                ws.send(JSON.stringify(message));
            }
        }
        catch (error) {
            wsLogger.error('Error sending message to client', error);
        }
    }
    /**
     * Broadcast message to all connected clients
     */
    broadcast(message) {
        if (!this.wss || this.clients.size === 0) {
            wsLogger.debug('No WebSocket clients to broadcast to');
            return;
        }
        wsLogger.info('Broadcasting message to clients', {
            type: message.type,
            clientCount: this.clients.size
        });
        const deadClients = [];
        this.clients.forEach((client) => {
            try {
                if (client.readyState === ws_1.WebSocket.OPEN) {
                    client.send(JSON.stringify(message));
                }
                else {
                    deadClients.push(client);
                }
            }
            catch (error) {
                wsLogger.error('Error broadcasting to client', error);
                deadClients.push(client);
            }
        });
        // Clean up dead connections
        deadClients.forEach((client) => {
            this.clients.delete(client);
        });
    }
    /**
     * Broadcast payment event to all clients
     */
    broadcastPaymentEvent(eventData) {
        this.broadcast({
            type: 'payment_event',
            data: eventData,
            timestamp: new Date().toISOString()
        });
    }
    /**
     * Broadcast transaction update to all clients
     */
    broadcastTransactionUpdate(transaction) {
        this.broadcast({
            type: 'transaction_update',
            data: {
                transactionId: transaction._id?.toString(),
                status: transaction.status,
                amount: transaction.amount,
                currency: transaction.currency,
                paymentMethod: transaction.paymentMethod,
                paymentProvider: transaction.paymentProvider,
                metadata: transaction.metadata,
                updatedAt: transaction.updatedAt
            },
            timestamp: new Date().toISOString()
        });
    }
    /**
     * Broadcast receipt generation event to all clients
     */
    broadcastReceiptEvent(eventData) {
        this.broadcast({
            type: 'receipt_event',
            data: eventData,
            timestamp: new Date().toISOString()
        });
    }
    /**
     * Broadcast Viva webhook event to all clients
     */
    broadcastVivaWebhookEvent(eventData) {
        this.broadcast({
            type: 'viva_webhook_event',
            data: eventData,
            timestamp: new Date().toISOString()
        });
    }
    /**
     * Send health check ping to all clients
     */
    pingClients() {
        if (this.clients.size === 0)
            return;
        wsLogger.debug('Sending ping to all clients', { clientCount: this.clients.size });
        const deadClients = [];
        this.clients.forEach((client) => {
            try {
                if (client.readyState === ws_1.WebSocket.OPEN) {
                    client.ping();
                }
                else {
                    deadClients.push(client);
                }
            }
            catch (error) {
                wsLogger.error('Error pinging client', error);
                deadClients.push(client);
            }
        });
        // Clean up dead connections
        deadClients.forEach((client) => {
            this.clients.delete(client);
        });
    }
    /**
     * Get current connection statistics
     */
    getStats() {
        return {
            connectedClients: this.clients.size,
            serverRunning: this.wss !== null
        };
    }
    /**
     * Generate unique client ID
     */
    generateClientId() {
        return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    /**
     * Close WebSocket server
     */
    close() {
        if (this.wss) {
            wsLogger.info('Closing WebSocket server');
            this.wss.close();
            this.wss = null;
            this.clients.clear();
        }
    }
}
exports.websocketService = new WebSocketService();
//# sourceMappingURL=websocketService.js.map