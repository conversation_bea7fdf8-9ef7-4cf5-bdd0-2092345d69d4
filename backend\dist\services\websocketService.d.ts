/**
 * WebSocket Service
 *
 * Handles real-time communication between backend and frontend
 * Broadcasts payment events, transaction updates, and other real-time data
 */
import { ITransaction } from '../models/Transaction.mongo';
export interface WebSocketMessage {
    type: string;
    data: any;
    timestamp: string;
}
export interface PaymentEventData {
    transactionId: string;
    orderCode?: string | number;
    status: string;
    amount?: number;
    currency?: string;
    paymentProvider: string;
    eventType: string;
    metadata?: Record<string, any>;
}
export interface ReceiptEventData {
    transactionId: string;
    receiptType: 'customer' | 'merchant';
    receiptContent: string;
    format: 'text' | 'html' | 'pdf';
}
declare class WebSocketService {
    private wss;
    private clients;
    /**
     * Initialize WebSocket server
     */
    initialize(server: any): void;
    /**
     * Set up event handlers for a WebSocket client
     */
    private setupClientHandlers;
    /**
     * Handle messages from WebSocket clients
     */
    private handleClientMessage;
    /**
     * Send message to a specific client
     */
    private sendToClient;
    /**
     * Broadcast message to all connected clients
     */
    broadcast(message: WebSocketMessage): void;
    /**
     * Broadcast payment event to all clients
     */
    broadcastPaymentEvent(eventData: PaymentEventData): void;
    /**
     * Broadcast transaction update to all clients
     */
    broadcastTransactionUpdate(transaction: ITransaction): void;
    /**
     * Broadcast receipt generation event to all clients
     */
    broadcastReceiptEvent(eventData: ReceiptEventData): void;
    /**
     * Broadcast Viva webhook event to all clients
     */
    broadcastVivaWebhookEvent(eventData: {
        eventType: number;
        eventDescription: string;
        transactionId: string;
        orderCode: number;
        amount: number;
        statusId: string;
        processed: boolean;
    }): void;
    /**
     * Send health check ping to all clients
     */
    pingClients(): void;
    /**
     * Get current connection statistics
     */
    getStats(): {
        connectedClients: number;
        serverRunning: boolean;
    };
    /**
     * Generate unique client ID
     */
    private generateClientId;
    /**
     * Close WebSocket server
     */
    close(): void;
}
export declare const websocketService: WebSocketService;
export {};
//# sourceMappingURL=websocketService.d.ts.map