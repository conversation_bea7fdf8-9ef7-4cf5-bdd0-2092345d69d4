{"name": "fastify-plugin", "version": "5.0.1", "description": "Plugin helper for Fastify", "main": "plugin.js", "type": "commonjs", "types": "types/plugin.d.ts", "scripts": {"lint": "standard", "test": "npm run test:unit && npm run test:typescript", "test:unit": "c8 --100 node --test", "test:coverage": "c8 node --test && c8 report --reporter=html", "test:typescript": "tsd"}, "repository": {"type": "git", "url": "git+https://github.com/fastify/fastify-plugin.git"}, "keywords": ["plugin", "helper", "fastify"], "author": "<PERSON> - @delvedor (http://delved.org)", "license": "MIT", "bugs": {"url": "https://github.com/fastify/fastify-plugin/issues"}, "homepage": "https://github.com/fastify/fastify-plugin#readme", "devDependencies": {"@fastify/pre-commit": "^2.1.0", "@fastify/type-provider-typebox": "^5.0.0-pre.fv5.1", "@types/node": "^22.0.0", "c8": "^10.1.2", "fastify": "^5.0.0", "proxyquire": "^2.1.3", "standard": "^17.1.0", "tsd": "^0.31.0"}}